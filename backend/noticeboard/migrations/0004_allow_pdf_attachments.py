# Generated by Django 5.2.3 on 2025-07-31 11:39

import django.core.validators
import noticeboard.models
from django.db import migrations, models


class Migration(migrations.Migration):

    dependencies = [
        ('noticeboard', '0003_remove_title_description_fields'),
    ]

    operations = [
        migrations.AlterField(
            model_name='noticeattachment',
            name='file',
            field=models.FileField(upload_to=noticeboard.models.upload_to_notice_attachments, validators=[django.core.validators.FileExtensionValidator(allowed_extensions=['jpg', 'jpeg', 'png', 'gif', 'bmp', 'tiff', 'webp', 'heic', 'heif', 'svg', 'eps', 'ai', 'cr2', 'cr3', 'nef', 'arw', 'dng', 'ico', 'apng', 'psd', 'exr', 'dds', 'pdf'])]),
        ),
    ]
